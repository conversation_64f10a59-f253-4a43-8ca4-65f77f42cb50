.voice-chatbot {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.voice-chatbot-layout {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 1rem;
  height: 100%;
  padding: 1rem;
  box-sizing: border-box;
}

.left-panel {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  height: 100%;
  min-height: 0;
}

.voice-panel-container {
  background: #1a2942;
  padding: 1rem;
  border-radius: 12px;
  min-height: 200px;
  color: #ffffff;
  margin-top: auto;
}

.chat-panel-container {
  flex: 1;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
  max-height: 100%;
}

.right-panel {
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
  gap: 1rem;
}

.conversation-history {
  flex: 1;
  overflow-y: auto;
  padding-right: 0.5rem;
  min-height: 0;
  max-height: calc(100% - 220px);
}

.history-messages {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 1rem;
}

.history-message {
  padding: 1rem;
  border-radius: 8px;
  background: #f8f9fa;
  max-width: 100%;
  word-wrap: break-word;
}

.history-message.user {
  border: 1px solid #bbdefb;
  background: #f8f9fa;
}

.history-message.assistant {
  background: #1a2942;
  color: #ffffff;
  border: none;
}

.history-message strong {
  display: block;
  margin-bottom: 0.5rem;
  color: inherit;
  font-weight: 600;
}

.history-message p {
  margin: 0;
  color: inherit;
  line-height: 1.6;
}
