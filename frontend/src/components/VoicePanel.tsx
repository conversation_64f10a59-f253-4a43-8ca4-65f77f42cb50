'use client';

import AuraCircle from './AuraCircle';

interface VoicePanelProps {
  isRecording: boolean;
  isProcessing: boolean;
  audioStream?: MediaStream | null;
}

const VoicePanel = ({
  isRecording,
  isProcessing,
  audioStream
}: VoicePanelProps) => {
  
  const getStatusText = () => {
    if (isProcessing) {
      return {
        title: "İşleniyor...",
        subtitle: "AI yanıtınızı hazırlıyor",
        color: "text-amber-400"
      };
    }
    
    if (isRecording) {
      return {
        title: "Dinliyorum",
        subtitle: "Space ile bitir • ESC ile iptal et",
        color: "text-blue-400"
      };
    }
    
    return {
      title: "Hazır",
      subtitle: "Space tuşuna basarak başlayın",
      color: "text-gray-300"
    };
  };

  const status = getStatusText();

  return (
    <div className="h-full flex flex-col bg-black/20 backdrop-blur-sm rounded-2xl border border-white/10">
      {/* Header */}
      <div className="p-6 border-b border-white/10 flex-shrink-0">
        <h2 className="text-xl font-semibold text-white mb-2">Sesli Asistan</h2>
        <p className="text-gray-300 text-sm">Mikrofon ile etkileşim kurun</p>
      </div>

      {/* Aura Circle Container with Status */}
      <div className="flex-1 flex flex-col items-center justify-center p-8 min-h-0">
        {/* Main Content Container */}
        <div className="flex flex-col items-center">
          {/* Aura Circle */}
          <div className="relative mb-16">
            <AuraCircle
              isRecording={isRecording}
              isProcessing={isProcessing}
              audioStream={audioStream}
            />

            {/* Status Section */}
            <div className="absolute -bottom-16 left-1/2 transform -translate-x-1/2 text-center z-[9999]">
              <h3 className={`text-lg font-medium ${status.color} mb-2 drop-shadow-lg`}>
                {status.title}
              </h3>
              <p className="text-gray-400 text-sm mb-4 drop-shadow-lg whitespace-nowrap">
                {status.subtitle}
              </p>

              {/* Keyboard Shortcut Info */}
              <p className="text-xs text-gray-500 drop-shadow-lg whitespace-nowrap">
                <kbd className="px-2 py-1 bg-gray-700 rounded text-xs">Space</kbd> ile konuş •
                <kbd className="px-2 py-1 bg-gray-700 rounded text-xs ml-1">ESC</kbd> ile iptal
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VoicePanel;
